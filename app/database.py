from datetime import datetime

from sqlalchemy import Column, DateTime, ForeignKey, Integer, String, Text, create_engine
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Session, relationship, sessionmaker

from app.config.logger_factory import get_logger
from app.config.settings import config

logger = get_logger(__name__)

Base = declarative_base()


class Job(Base):
    __tablename__ = "jobs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    uuid = Column(String(36), unique=True, nullable=False, index=True)
    state = Column(String(50), nullable=False, index=True)
    step = Column(String(50), nullable=True)
    task_id = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=False, index=True)
    ot_traceid = Column(String(255), nullable=True)
    ot_spanid = Column(String(255), nullable=True)
    error_message = Column(Text, nullable=True)

    results = relationship("JobResult", back_populates="job")

    def __repr__(self):
        return f"<Job(uuid='{self.uuid}', state='{self.state}', step='{self.step}')>"


class JobResult(Base):
    __tablename__ = "job_results"

    id = Column(Integer, primary_key=True, index=True)
    job_uuid = Column(String(36), ForeignKey("jobs.uuid"), nullable=False)
    result = Column(JSONB, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    job = relationship("Job", back_populates="results")


class DatabaseManager:
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self._init_database()

    def _init_database(self):
        try:
            host = config.get("postgresql.host", "postgresql")
            port = config.get("postgresql.port", 5432)
            username = config.get("postgresql.username", "postgres")
            password = config.get("postgresql.password", "password")
            database = config.get("postgresql.database", "object_detection")
            echo_value = config.get("postgresql.echo", False)

            connection_string = f"postgresql://{username}:{password}@{host}:{port}/{database}"

            self.engine = create_engine(connection_string, pool_pre_ping=True, pool_recycle=300, echo=echo_value)

            self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

            Base.metadata.create_all(bind=self.engine)

            logger.info("PostgreSQL database manager initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL database manager: {str(e)}")
            raise

    def get_session(self) -> Session:
        return self.SessionLocal()

    def close(self):
        if self.engine:
            self.engine.dispose()


db_manager = DatabaseManager()


def get_db() -> Session:
    db = db_manager.get_session()
    try:
        yield db
    finally:
        db.close()
