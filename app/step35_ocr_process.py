import base64
import io
import json
import logging
import math
import os
import random
import string
import sys

import cv2
import numpy as np
import requests
from PIL import Image

from app.config.logging_config import setup_logging

setup_logging()
logger = logging.getLogger(__name__)


def ocr_plate(license_plate_crop, url):
    try:
        with Image.fromarray(license_plate_crop) as img:
            buffered = io.BytesIO()
            img.save(buffered, format="PNG")
            img_byte_array = buffered.getvalue()

        base64_encoded = base64.b64encode(img_byte_array)
        base64_string = base64_encoded.decode("utf-8")
        base64_len = str(len(base64_encoded))

        payload = {
            "base64": base64_string,
            "trim": "\n",
            "languages": "eng",
            "whitelist": "ABCDEFGHIJKLMNOPQRSTUXVYZ1234567890",
        }
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Accept": "*/*",
            "Content-Length": base64_len,
        }

        response = requests.request("POST", url, headers=headers, data=json.dumps(payload))
        if response.status_code == 200:
            result_json = response.json()
            result = result_json.get("result")

            stringStatus = False
            numericStatus = False
            for tchar in result:
                if tchar.isalpha():
                    stringStatus = True
                elif tchar.isdigit():
                    numericStatus = True

            if not stringStatus or not numericStatus:
                return ""

            if "\n" in result or "\t" in result or "\r" in result:
                return ""

            result = result.replace(" ", "")

            if len(result) > 4:
                return result.strip()
            else:
                return ""
        else:
            return ""
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"Error step35_ocr_process ocr_plate detection:{exc_type} {fname} {exc_tb.tb_lineno} {e}"
        raise Exception(err_msg)


def oalpr_plate(license_plate_crop, uuid, country, url):
    try:
        with Image.fromarray(license_plate_crop) as img:
            buffered = io.BytesIO()
            img.save(buffered, format="PNG")
            img_byte_array = buffered.getvalue()

        base64_encoded = base64.b64encode(img_byte_array)
        base64_string = base64_encoded.decode("utf-8")

        random_string = "".join(random.choices(string.ascii_letters + string.digits, k=10))
        procid = uuid + random_string

        payload = {"base64": base64_string, "country": country, "procid": procid}
        headers = {"Content-Type": "application/json"}

        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 200:
            result_json = response.json()
            """
            {'version': 2, 'data_type': 'alpr_results', 'epoch_time': 1735037168470, 'img_width': 193, 'img_height': 68,
             'processing_time_ms': 12.589959, 'regions_of_interest': [{'x': 0, 'y': 0, 'width': 193, 'height': 68}],
             'results': [{'plate': 'RI83JF', 'confidence': 86.123802, 'matches_template': 0, 'plate_index': 0, 'region': '',
                          'region_confidence': 0, 'processing_time_ms': 11.773791, 'requested_topn': 1,
                          'coordinates': [{'x': 26, 'y': 14}, {'x': 190, 'y': 9}, {'x': 191, 'y': 46}, {'x': 27, 'y': 51}],
                          'candidates': [{'plate': 'RI83JF', 'confidence': 86.123802, 'matches_template': 0}]}]}
            """
            result = result_json.get("results")
            if len(result) > 0:
                return result[0]["plate"]
            else:
                return ""
        else:
            return ""
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"Error step35_ocr_process oalpr_plate detection:{exc_type} {fname} {exc_tb.tb_lineno} {e}"
        raise Exception(err_msg)


def rotate_image(image, angle):
    try:
        image_center = tuple(np.array(image.shape[1::-1]) / 2)
        rot_mat = cv2.getRotationMatrix2D(image_center, angle, 1.0)
        result = cv2.warpAffine(image, rot_mat, image.shape[1::-1], flags=cv2.INTER_LINEAR)
        return result
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        logger.error(f"Error step26_before_ocr rotate_image:{exc_type} {fname} {exc_tb.tb_lineno} {e}")


def compute_skew(src_img):
    try:
        if len(src_img.shape) == 3:
            h, w, _ = src_img.shape
        elif len(src_img.shape) == 2:
            h, w = src_img.shape
        else:
            print("upsupported image type")

        img = cv2.medianBlur(src_img, 3)

        edges = cv2.Canny(img, threshold1=30, threshold2=100, apertureSize=3, L2gradient=True)
        lines = cv2.HoughLinesP(edges, 1, math.pi / 180, 30, minLineLength=w / 4.0, maxLineGap=h / 4.0)
        angle = 0.0

        if lines is None:
            return 0.0
        else:
            cnt = 0
            for x1, y1, x2, y2 in lines[0]:
                ang = np.arctan2(y2 - y1, x2 - x1)
                # print(ang)
                if math.fabs(ang) <= 30:  # excluding extreme rotations
                    angle += ang
                    cnt += 1

            if cnt == 0:
                return 0.0
        return (angle / cnt) * 180 / math.pi
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        logger.error(f"Error step26_before_ocr compute_skew:{exc_type} {fname} {exc_tb.tb_lineno} {e}")


def img_process(license_plate_crop):
    try:
        process_image = None
        with Image.fromarray(license_plate_crop) as img:
            buffered = io.BytesIO()
            img.save(buffered, format="PNG")
            img_byte_array = buffered.getvalue()

            nparr = np.frombuffer(img_byte_array, np.uint8)
            imgx = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            # deskew
            deskew_image = rotate_image(imgx, compute_skew(imgx))

            # gray
            gray_image = cv2.cvtColor(deskew_image, cv2.COLOR_BGR2GRAY)

            # sharpened
            kernel = np.array([[0, -1, 0], [-1, 5, -1], [0, -1, 0]])
            sharpened_image = cv2.filter2D(gray_image, -1, kernel)

            # brightness/contrast
            alpha = 1.5
            beta = 30
            brightness_contrast_image = cv2.convertScaleAbs(sharpened_image, alpha=alpha, beta=beta)

            # noise reduction
            noise_reduction_image = cv2.GaussianBlur(brightness_contrast_image, (5, 5), 0)

            # threshold
            _, threshold_image = cv2.threshold(noise_reduction_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            process_image = threshold_image

        return process_image
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"Error step26_before_ocr img_process:{exc_type} {fname} {exc_tb.tb_lineno} {e}"
        raise Exception(err_msg)
