import logging
import os
import sys

import cv2
import numpy as np
import supervision as sv

from app.config.logger_factory import get_logger
from app.helper import typed_append

logger = get_logger(__name__)


def frame_difference(frame1, frame2):
    diff = cv2.absdiff(frame1, frame2)
    return diff


def get_frames(video_path, frame_diff_count):
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    frame_start = frame_diff_count
    frame_end = total_frames - frame_diff_count

    def get_frame(frame_index):
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_index)
        ret, frame = cap.read()
        if not ret:
            return None

        return frame

    frame1 = get_frame(frame_start)
    frame2 = get_frame(frame_end)

    return frame1, frame2


def crop_brightest_region(img):
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    blurred = cv2.GaussianBlur(gray, (21, 21), 0)
    (minVal, maxVal, minLoc, maxLoc) = cv2.minMaxLoc(blurred)

    box_w, box_h = 100, 150
    x1 = max(maxLoc[0] - box_w // 2, 0)
    y1 = max(maxLoc[1] - box_h // 2, 0)
    x2 = min(x1 + box_w, img.shape[1])
    y2 = min(y1 + box_h, img.shape[0])

    cropped = img[y1:y2, x1:x2]
    return cropped


def crop_polygon_zone_from_video(video_path, zone, frame_index):
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    frame_start_index = frame_index
    frame_end_index = total_frames - frame_index

    polygon_np = np.array(zone.polygon, dtype=np.int32).reshape((-1, 1, 2))

    # start
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_start_index)
    _, frame_start = cap.read()

    mask_start = np.zeros(frame_start.shape[:2], dtype=np.uint8)

    cv2.fillPoly(mask_start, [polygon_np], color=255)

    masked_start = cv2.bitwise_and(frame_start, frame_start, mask=mask_start)

    x, y, w, h = cv2.boundingRect(polygon_np)
    cropped_start = masked_start[y : y + h, x : x + w]

    # end
    cap.set(cv2.CAP_PROP_POS_FRAMES, frame_end_index)
    _, frame_end = cap.read()

    mask_end = np.zeros(frame_end.shape[:2], dtype=np.uint8)
    cv2.fillPoly(mask_end, [polygon_np], color=255)
    masked_end = cv2.bitwise_and(frame_end, frame_end, mask=mask_end)

    x, y, w, h = cv2.boundingRect(polygon_np)
    cropped_end = masked_end[y : y + h, x : x + w]

    cap.release()

    return cropped_start, cropped_end


def step33_package_detection_diff_frame_run(job_uuid, model, video_path, hotzone_zones_polygon, conf, device):
    try:
        import gc

        import torch

        torch.cuda.empty_cache()
        gc.collect()

        frame_diff_count = 10

        cropped_frame = None
        if len(hotzone_zones_polygon) > 0:
            for zone in hotzone_zones_polygon:
                frame1, frame2 = crop_polygon_zone_from_video(video_path, zone, frame_diff_count)
                diff_frame = frame_difference(frame1, frame2)
                cropped_frame = crop_brightest_region(diff_frame)
        else:
            frame1, frame2 = get_frames(video_path, frame_diff_count)
            diff_frame = frame_difference(frame1, frame2)
            cropped_frame = crop_brightest_region(diff_frame)

        if cropped_frame is None:
            return False, "No Brightest Region", {}

        visualization_args = False
        total_found_classes = {}
        results_standard = model.predict(
            cropped_frame,
            save=False,
            imgsz=640,
            conf=conf,
            stream=False,
            device=device,
            verbose=False,
            show=visualization_args,
            show_labels=visualization_args,
            show_conf=visualization_args,
            show_boxes=visualization_args,
            stream_buffer=True,
            visualize=False,
            augment=False,
            agnostic_nms=False,
            retina_masks=False,
            save_crop=False,
        )

        for rstandard in results_standard:
            result_detections = ()
            detections = sv.Detections.from_ultralytics(rstandard)
            result_detections = typed_append(result_detections, detections)

            for result_detection in result_detections:
                if result_detection is not None and len(result_detection.confidence) > 0:
                    confIndx = 0

                    # FOUND
                    for current_conf in result_detection.confidence:
                        current_conf = round(float(current_conf), 3)
                        found_class = result_detection["class_name"][confIndx]
                        total_found_classes[found_class] = current_conf
                        break
                    break

        return total_found_classes
    except Exception as err:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"step33_package_detection_diff_frame_run Exception with error:{err} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        raise Exception(err_msg)
