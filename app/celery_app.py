import os

from celery import Celery

from app.config.logger_factory import get_logger, set_logging_context
from app.config.settings import config
from app.job_tracking import job_tracker
from app.models import TaskParameters
from app.step10_downloadfile import step10_downloadFile_run
from app.step30_before_check import step30_before_check_run
from app.step40_sendresult import step40_sendResult_run
from app.step50_cleanEnvironment import step50_cleanEnvironment_run

logger = get_logger(__name__)


def make_celery():
    celery_app = Celery("object_detection_worker")

    broker_url = ""
    result_broker_url = ""

    rabbitmq_username = config.get("rabbitmq.username", "rabbitusr")
    rabbitmq_password = config.get("rabbitmq.password", "1lqgEJU3VPyhg")
    rabbitmq_host = config.get("rabbitmq.host", "rabbitmq")
    rabbitmq_port = config.get("rabbitmq.port", 5672)
    rabbitmq_vhost = config.get("rabbitmq.vhost", "/")
    broker_url = f"amqp://{rabbitmq_username}:{rabbitmq_password}@{rabbitmq_host}:{rabbitmq_port}{rabbitmq_vhost}"

    postgresql_username = config.get("postgresql.username", "postgres")
    postgresql_password = config.get("postgresql.password", "4GfW42eVb")
    postgresql_host = config.get("postgresql.host", "postgresql")
    postgresql_port = config.get("postgresql.port", 5432)
    postgresql_database = config.get("postgresql.database", "object_detection")
    result_broker_url = f"db+postgresql://{postgresql_username}:{postgresql_password}@{postgresql_host}:{postgresql_port}/{postgresql_database}"

    if broker_url != "" and result_broker_url != "":
        celery_app.conf.update(
            broker_url=broker_url,
            result_backend=result_broker_url,
            task_serializer="json",
            accept_content=["json"],
            result_serializer="json",
            timezone="UTC",
            enable_utc=True,
            task_routes={"app.celery_app.process_video": {"queue": "video_processing"}},
            worker_prefetch_multiplier=config.get("worker.prefetch_multiplier", 1),
            worker_concurrency=config.get("worker.concurrency", 4),
            worker_max_tasks_per_child=config.get("worker.max_tasks_per_child", 1000),
            worker_log_format="[%(asctime)s: %(levelname)s/%(processName)s] %(message)s",
            worker_task_log_format="[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s",
            worker_hijack_root_logger=False,
            worker_log_color=False,
            task_acks_late=True,
            task_reject_on_worker_lost=True,
            task_send_sent_event=True,
            task_track_started=True,
            task_ignore_result=False,
            worker_disable_rate_limits=True,
        )

        return celery_app
    else:
        return None


celery_app = make_celery()

# class QuietTask(Task):
#     def on_failure(self, exc, task_id, args, kwargs, einfo):
#         if isinstance(exc, (SystemExit, KeyboardInterrupt, MemoryError)):
#             logger.critical(f"Critical error in task {self.name}[{task_id}]: {exc}")
#         else:
#             logger.error(f"Task {self.name}[{task_id}] failed: {str(exc)}")
#
#     def on_retry(self, exc, task_id, args, kwargs, einfo):
#         """Handle task retry with minimal logging."""
#         # logger.warning(f"Task {self.name}[{task_id}] retry: {str(exc)[:100]}...")
#         logger.warning(f"Task {self.name}[{task_id}] retry: {str(exc)}")
#
#     def on_success(self, retval, task_id, args, kwargs):
#         """Handle task success with minimal logging."""
#         logger.info(f"Task {self.name}[{task_id}] completed successfully")
#
#     def apply_async(self, args=None, kwargs=None, **options):
#         """Override to suppress some connection warnings."""
#         try:
#             return super().apply_async(args, kwargs, **options)
#         except Exception as e:
#             if "connection" in str(e).lower() or "broker" in str(e).lower():
#                 # logger.warning(f"Connection issue in {self.name}: {str(e)[:100]}...")
#                 logger.warning(f"Connection issue in {self.name}: {str(e)}")
#             else:
#                 # logger.error(f"Error in {self.name}: {str(e)[:1200]}...")
#                 logger.error(f"Error in {self.name}: {str(e)}")
#             raise


@celery_app.task(bind=True, autoretry_for=(Exception,), max_retries=1, retry_backoff=True)
def process_video(self, job_uuid: str, task_params_dict: dict) -> dict:
    job_tracker.update_job_state(job_uuid, "process", "started", None, self.request.id)
    task_params = None
    try:
        task_params = TaskParameters(**task_params_dict)
        # Set logging context for this Celery task
        set_logging_context(job_uuid=job_uuid, trace_id=task_params.ot.trace_id, span_id=task_params.ot.span_id)
    except Exception as e:
        error_msg = f"Failed to prepare task parameters: {str(e)}"
        logger.error(error_msg)
        job_tracker.update_job_state(job_uuid, "taskParameters", "failed", error_msg)

        # ??????????
        # bu donusum hangi durumda hata verir????
        # step40_sendResult_run(job_uuid, None, None, "failed", error_msg)

    temp_base_dir = config.get("file_processing.video_temp_directory", "/tmp")
    video_path = os.path.join(temp_base_dir, f"{job_uuid}.video.mp4")
    video_url = ""
    if task_params is not None:
        video_url = task_params.resources.url

    try:
        # Pre Process
        # this step is added for tasks that may need to be done first of all.
        # from app.step00_preprocess import step00_preprocess_run
        # step00_preprocess_run()

        # Download Video
        try:
            step10_downloadFile_run(job_uuid, video_url, video_path)
        except Exception as e:
            error_msg = f"Download video failed err:{str(e)}"
            logger.error(error_msg)
            job_tracker.update_job_state(job_uuid, "downloafile", "failed", error_msg)
            step40_sendResult_run(job_uuid, task_params, "failed", error_msg)
            raise

        # File Validation
        # this step is added to check if the downloaded file is a video file
        # from app.step20_filevalidate import step20_filevalidate_run
        # step20_filevalidate_run()

        # Object Detection
        try:
            step30_before_check_run(job_uuid, task_params_dict, video_path)
        except Exception as e:
            error_msg = f"Object detection failed err:{str(e)}"
            logger.error(error_msg)
            job_tracker.update_job_state(job_uuid, "process", "failed", error_msg)
            step40_sendResult_run(job_uuid, task_params, "failed", error_msg)
            raise

        # Send Results
        try:
            job_state = job_tracker.get_job_state_by_uuid(job_uuid)
            if job_state:
                step40_sendResult_run(job_uuid, task_params, job_state, None)
            else:
                raise Exception("Job state not found")
        except Exception as e:
            error_msg = f"Send results failed {str(e)}"
            logger.error(error_msg)
            job_tracker.update_job_state(job_uuid, "sendresult", "failed", error_msg)
            step40_sendResult_run(job_uuid, task_params, "failed", error_msg)
            raise

        # Clean Environment
        try:
            step50_cleanEnvironment_run(job_uuid, task_params, video_path)
        except Exception as e:
            error_msg = f"Clean up env failed err:{str(e)}"
            logger.error(error_msg)
            job_tracker.update_job_state(job_uuid, "sendresult", "failed", error_msg)
            raise

        return {"status": "success", "uuid": job_uuid}
    except Exception as e:
        error_msg = f"Video processing failed for UUID {job_uuid}: {str(e)}"
        logger.error(error_msg)
        job_tracker.update_job_state(job_uuid, "process", "failed", error_msg)
        raise
    finally:
        job_tracker.update_job_state(job_uuid, "process", "completed")


if __name__ == "__main__":
    celery_app.start()
