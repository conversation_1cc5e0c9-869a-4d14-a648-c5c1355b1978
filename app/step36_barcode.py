import logging
import os
import sys

import cv2 as cv

from app.config.logging_config import setup_logging
from app.helper import prepare_detection_result

setup_logging()
logger = logging.getLogger(__name__)


def step36_barcode_run(job_uuid, source, barcode_detection_filter_items):
    try:
        qr_decoder = cv.QRCodeDetector()
        cap = cv.VideoCapture(source)
        qrCodes = []
        while cap.isOpened():
            ret, frame = cap.read()
            if ret:
                retval, decoded_info, points, straight_qrcode = qr_decoder.detectAndDecodeMulti(frame)
                if retval:
                    if len(decoded_info) > 0:
                        for qrc in decoded_info:
                            qrcn = qrc.strip()
                            if len(qrcn) > 0:
                                qrCodes.append(qrcn)
            else:
                cap.release()

        cap.release()

        qrCodesU = set(qrCodes)
        for qr in qrCodesU:
            if barcode_detection_filter_items is not None:
                if qr.lower() in [item.lower() for item in barcode_detection_filter_items]:
                    prepare_detection_result(job_uuid, qr, "barcode")
            else:
                prepare_detection_result(job_uuid, qr, "barcode")
    except Exception as err:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"step36_barcode_run Exception with error:{err} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        raise Exception(err_msg)
