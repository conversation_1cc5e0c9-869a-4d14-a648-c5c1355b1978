#!/usr/bin/env python3
"""
Test script for the new centralized logging system.
This script demonstrates the new logging features.
"""

import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config.logger_factory import get_logger, set_logging_context, LoggingContext

def test_basic_logging():
    """Test basic logging functionality."""
    logger = get_logger("test_module")
    
    print("=== Basic Logging Test ===")
    logger.info("This is a basic info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    print()

def test_context_logging():
    """Test context-aware logging."""
    logger = get_logger("test_context")
    
    print("=== Context Logging Test ===")
    
    # Set context manually
    set_logging_context(job_uuid="test-job-123", trace_id="trace-456")
    logger.info("This message should include job_uuid and trace_id")
    
    # Test with context manager
    with LoggingContext(job_uuid="context-job-789", trace_id="context-trace-abc"):
        logger.info("This message is within a logging context")
        logger.error("Error within context")
    
    # After context manager, should revert to previous context
    logger.info("This should still have the original context")
    print()

def test_multiple_loggers():
    """Test multiple loggers with different names."""
    logger1 = get_logger("module1")
    logger2 = get_logger("module2")
    
    print("=== Multiple Loggers Test ===")
    
    set_logging_context(job_uuid="multi-test-job", trace_id="multi-trace")
    
    logger1.info("Message from module1")
    logger2.info("Message from module2")
    logger1.warning("Warning from module1")
    logger2.error("Error from module2")
    print()

if __name__ == "__main__":
    print("Testing the new centralized logging system...\n")
    
    test_basic_logging()
    test_context_logging()
    test_multiple_loggers()
    
    print("Logging tests completed!")
