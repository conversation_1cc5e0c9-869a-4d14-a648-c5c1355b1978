import logging
import os
import sys
import urllib

import requests

from app.config.logger_factory import get_logger
from app.job_tracking import job_tracker

logger = get_logger(__name__)


def step10_downloadFile_run(job_uuid: str, resource_url: str, video_path: str):
    try:
        job_tracker.update_job_state(job_uuid, "downloadfile", "started")
        if resource_url == "":
            raise Exception("Resource URL is empty")
        else:
            logger.info(f"Downloading video from {resource_url} to {video_path} {job_uuid}")
            try:
                with requests.get(resource_url, allow_redirects=True, stream=True) as responseGet:
                    if responseGet.ok:
                        responseGet.raise_for_status()
                        try:
                            with open(video_path, "wb") as out_file:
                                for chunk in responseGet.iter_content(chunk_size=1024 * 1024 * 2):
                                    out_file.write(chunk)
                        except Exception as e:
                            raise Exception(f"downloadFile Request status_code:{responseGet.status_code} reason:{responseGet.reason} url:{resource_url} job_uuid:{job_uuid} {str(e)}")
                    else:
                        raise Exception(f"downloadFile Request status_code:{responseGet.status_code} reason:{responseGet.reason} url:{resource_url} job_uuid:{job_uuid}")

            except urllib.error.URLError as e:
                raise Exception(f"Failed to download video: {str(e)}")
            except Exception as e:
                raise Exception(f"downloadFile Unexpected error during video download: {str(e)}")

        if not os.path.exists(video_path):
            raise Exception("Video file was not downloaded successfully")

        file_size = os.path.getsize(video_path)
        if file_size == 0:
            raise Exception("Downloaded video file is empty")

        logger.debug(f"Video downloaded successfully to {video_path} (size: {file_size} bytes) {job_uuid}")
    except Exception as err:
        job_tracker.update_job_state(job_uuid, "downloadfile", "failed")
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"step10_downloadFile_run Exception with error:{err} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        raise Exception(err_msg)
    finally:
        job_tracker.update_job_state(job_uuid, "downloadfile", "finished")
