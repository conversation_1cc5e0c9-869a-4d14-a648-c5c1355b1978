import json
import logging
import os
import sys
from typing import Optional

from fastapi import Depends, <PERSON>AP<PERSON>, <PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from app.celery_app import process_video
from app.config.logger_factory import get_logger, set_logging_context
from app.config.settings import config
from app.job_tracking import job_tracker
from app.models import DetectionRequest, DetectionResponse, ErrorResponse, JobHistoryResponse, JobListResponse, QueueStatsResponse, TaskParameters, WorkerInfoResponse
from app.request_validation import validate_request, verify_auth_token

app = FastAPI(
    title="Object Detection Service",
    description="A service for detecting person, animals, vehicles, packages, faces, license plates, and barcodes in videos",
    version="1.0.0",
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger = get_logger(__name__)


@app.post(
    "/object_detection",
    response_model=DetectionResponse,
    responses={409: {"model": ErrorResponse}},
)
async def detect_objects(request: DetectionRequest, authorization: Optional[str] = Header(None)):
    response: JSONResponse = validate_request(request, authorization)
    content_bytes = response.body
    content_dict = json.loads(content_bytes)
    if "status_code" in content_dict:
        return response

    task_params = TaskParameters(**content_dict)
    job_uuid = task_params.file.uuid

    # Set logging context for this HTTP request
    set_logging_context(
        job_uuid=job_uuid,
        trace_id=task_params.ot.trace_id,
        span_id=task_params.ot.span_id
    )

    try:
        # task_params.ot.span_id us actually parent_span_id
        job_tracker.create_job(job_uuid, task_params.ot.trace_id, task_params.ot.span_id)

        task = process_video.apply_async(kwargs={"job_uuid": job_uuid, "task_params_dict": task_params.model_dump()})
        task_id = task.id

        job_tracker.update_job_state(job_uuid, "process", "queued", None, task_id)

        return DetectionResponse(
            status="queued",
            status_code=200,
            task_id=task_id,
            uuid=job_uuid,
            message="job added successfully",
        )
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        respmes = f"queue task Exception with error: {e} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        logger.error(respmes)
        return DetectionResponse(
            status="queued",
            status_code=200,
            task_id="",
            uuid="",
            message="job added successfully!!!",
        )


@app.get("/stats/queue", response_model=QueueStatsResponse)
async def get_queue_stats(authorized: bool = Depends(verify_auth_token)):
    # In a real implementation, we would get this from Celery/RabbitMQ
    queued_count = job_tracker.get_job_count_by_state("queued")
    return QueueStatsResponse(count=queued_count)


@app.get("/stats/queue/list", response_model=JobListResponse)
async def get_queue_list(authorized: bool = Depends(verify_auth_token)):
    # In a real implementation, we would get this from Celery/RabbitMQ
    queued_jobs = job_tracker.get_jobs_by_state("queued")
    # Convert datetime objects to strings for JSON serialization
    for job in queued_jobs:
        if "created_at" in job and hasattr(job["created_at"], "isoformat"):
            job["created_at"] = job["created_at"].isoformat()
    return JobListResponse(jobs=queued_jobs)


@app.get("/stats/completed", response_model=QueueStatsResponse)
async def get_completed_stats(authorized: bool = Depends(verify_auth_token)):
    completed_count = job_tracker.get_job_count_by_state("completed")
    return QueueStatsResponse(count=completed_count)


@app.get("/stats/failed", response_model=QueueStatsResponse)
async def get_failed_stats(authorized: bool = Depends(verify_auth_token)):
    failed_count = job_tracker.get_job_count_by_state("failed")
    return QueueStatsResponse(count=failed_count)


@app.get("/stats/processing", response_model=QueueStatsResponse)
async def get_processing_stats(authorized: bool = Depends(verify_auth_token)):
    # In a real implementation, we would get this from Celery/RabbitMQ
    processing_count = job_tracker.get_job_count_by_state("processing")
    return QueueStatsResponse(count=processing_count)


@app.get("/stats/workers", response_model=WorkerInfoResponse)
async def get_worker_info(authorized: bool = Depends(verify_auth_token)):
    # In a real implementation, we would get this from Celery
    # For now, return empty list
    return WorkerInfoResponse(workers=[])


@app.get("/stats/job/{job_uuid}", response_model=JobHistoryResponse)
async def get_job_history(job_uuid: str, authorized: bool = Depends(verify_auth_token)):
    # In a real implementation, we would get this from job tracking storage
    job = job_tracker.get_job(job_uuid)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")

    # Convert datetime objects to strings for JSON serialization
    if "created_at" in job and hasattr(job["created_at"], "isoformat"):
        job["created_at"] = job["created_at"].isoformat()
    if "updated_at" in job and hasattr(job["updated_at"], "isoformat"):
        job["updated_at"] = job["updated_at"].isoformat()
    if "expires_at" in job and hasattr(job["expires_at"], "isoformat"):
        job["expires_at"] = job["expires_at"].isoformat()

    return JobHistoryResponse(job=job)


@app.get("/health")
async def health_check():
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "app.main:app",
        host=config.get("api.host", "0.0.0.0"),
        port=config.get("api.port", 8000),
        reload=config.get("api.reload", True),
    )
