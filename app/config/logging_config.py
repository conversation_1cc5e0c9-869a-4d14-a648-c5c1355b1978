import logging
from logging.handlers import RotatingFileHandler

from app.config.settings import config


def setup_logging():
    log_level = config.get("logging.level", "INFO")
    log_format = config.get("logging.format", "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s")
    log_file_path = config.get("logging.file_path", "")
    max_file_size_mb = config.get("logging.max_file_size_mb", 10)
    backup_count = config.get("logging.backup_count", 5)

    formatter = logging.Formatter(log_format)

    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    celery_loggers = [
        "celery",
        "celery.worker",
        "celery.task",
        "celery.redirected",
        "celery.worker.strategy",
        "celery.worker.consumer",
        "celery.worker.heartbeat",
        "celery.worker.control",
        "celery.bootsteps",
        "kombu",
        "amqp",
    ]

    for logger_name in celery_loggers:
        celery_logger = logging.getLogger(logger_name)
        celery_logger.setLevel(logging.WARNING)
        celery_logger.propagate = False

    if log_file_path:
        try:
            file_handler = RotatingFileHandler(
                log_file_path,
                maxBytes=max_file_size_mb * 1024 * 1024,
                backupCount=backup_count,
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        except Exception as e:
            console_handler.setLevel(logging.WARNING)
            root_logger.warning(f"Failed to setup file logging: {e}")

    return root_logger
