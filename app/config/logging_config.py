"""
Legacy logging configuration module.
This module is kept for backward compatibility.
New code should use app.config.logger_factory instead.
"""

import logging
import warnings
from app.config.logger_factory import logger_factory, get_logger


def setup_logging():
    """
    Legacy function for setting up logging.

    This function is deprecated. Use logger_factory directly instead.
    It now delegates to the new LoggerFactory for consistency.
    """
    warnings.warn(
        "setup_logging() is deprecated. Use 'from app.config.logger_factory import get_logger' instead.",
        DeprecationWarning,
        stacklevel=2
    )

    # Initialize the factory (this is idempotent)
    logger_factory._setup_root_logger()

    return logging.getLogger()
