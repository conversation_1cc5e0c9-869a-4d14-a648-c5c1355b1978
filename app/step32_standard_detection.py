import logging
import os
import sys

import supervision as sv

from app.config.logging_config import setup_logging
from app.helper import find_class, get_total_results, prepare_detection_result, typed_append

setup_logging()
logger = logging.getLogger(__name__)


def step32_standard_detection_run(
    job_uuid,
    model,
    mediasource,
    hotzone_zones_polygon,
    classes_index,
    conf,
    standard_skip_frames,
    device,
    person_detection_classes,
    vehicle_detection_classes,
    animal_detection_classes,
):
    try:
        import gc

        import torch

        torch.cuda.empty_cache()
        gc.collect()

        visualization_args = False
        total_found_classes = {}

        batch_size = 4

        results_standard = model.predict(
            mediasource,
            save=False,
            imgsz=640,
            conf=conf,
            stream=True,
            batch=batch_size,
            vid_stride=standard_skip_frames,
            device=device,
            verbose=False,
            show=visualization_args,
            show_labels=visualization_args,
            show_conf=visualization_args,
            show_boxes=visualization_args,
            classes=classes_index,
            stream_buffer=False,
            visualize=False,
            augment=False,
            agnostic_nms=False,
            retina_masks=False,
            save_crop=False,
        )
        for rstandard in results_standard:
            result_detections = ()
            detections = sv.Detections.from_ultralytics(rstandard)
            if len(hotzone_zones_polygon) > 0:
                for zone in hotzone_zones_polygon:
                    mask = zone.trigger(detections=detections)
                    zone_detections = detections[mask & (detections.confidence >= conf)]
                    result_detections = typed_append(result_detections, zone_detections)
            else:
                result_detections = typed_append(result_detections, detections)

            for result_detection in result_detections:
                if result_detection is not None and len(result_detection.confidence) > 0:
                    confIndx = 0
                    for current_conf in result_detection.confidence:
                        current_conf = round(float(current_conf), 3)
                        found_class = result_detection["class_name"][confIndx]
                        confIndx = confIndx + 1

                        if found_class in total_found_classes:
                            if current_conf > total_found_classes[found_class]:
                                total_found_classes[found_class] = current_conf
                        else:
                            total_found_classes[found_class] = current_conf

        standard_total_found_classes = {}
        standard_total_found_classes = get_total_results(standard_total_found_classes, total_found_classes)

        for standard_label in standard_total_found_classes:
            standard_label_category = find_class(
                standard_label,
                personClasses=person_detection_classes,
                vehicleClasses=vehicle_detection_classes,
                animalClasses=animal_detection_classes,
            )
            prepare_detection_result(job_uuid, standard_label, standard_label_category)

    except Exception as err:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        err_msg = f"step32_standard_detection_run Exception with error:{err} file:{fname} line:{exc_tb.tb_lineno} exc_info:{sys.exc_info()}"
        raise Exception(err_msg)
