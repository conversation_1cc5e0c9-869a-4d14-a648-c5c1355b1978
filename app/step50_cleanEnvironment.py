import os

from app.config.logger_factory import get_logger
from app.job_tracking import job_tracker
from app.models import TaskParameters

logger = get_logger(__name__)


def step50_cleanEnvironment_run(job_uuid, task_params: TaskParameters, video_path):
    job_tracker.update_job_state(job_uuid, "cleanenv", "started")
    try:
        if os.path.exists(video_path):
            os.remove(video_path)
    except Exception as e:
        job_tracker.update_job_state(job_uuid, "cleanenv", "failed")
        raise Exception(f"Unexpected error while clean environment {str(e)}")
    finally:
        job_tracker.update_job_state(job_uuid, "cleanenv", "finished")
